name: Compile AHK 2.0 and Release

on:
  workflow_dispatch:
    inputs:
      version_override:
        description: "Override version from AHK script (e.g., v1.0.0)"
        required: false
        type: string

jobs:
  build-and-release:
    runs-on: windows-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 获取所有历史，包括标签

      - name: Extract Version from AHK script
        id: get_version
        shell: pwsh
        run: |
          $scriptPath = "DoroHelper.ahk" # 确保这个路径是正确的
          $version = "${{ github.event.inputs.version_override }}"
          if ([string]::IsNullOrWhiteSpace($version)) {
            if (-not (Test-Path $scriptPath)) {
              Write-Warning "Script file '${scriptPath}' not found for version extraction. Current directory: $(Get-Location). Using default '0.0.0'."
              $version = "0.0.0"
            } else {
              $content = Get-Content -Path $scriptPath -Raw
              $match = $content | Select-String -Pattern 'currentVersion\s*:=\s*"([^"]+)"'
              if ($match) {
                $version = $match.Matches[0].Groups[1].Value
                Write-Host "Version found in AHK script: ${version}"
              } else {
                Write-Warning "Version pattern (currentVersion := \"...\") not found in ${scriptPath}.Using default '0.0.0'."
                $version = "0.0.0"
              }
            }
          } else {
            Write-Host "Using overridden version: ${version}"
          }

          if (-not $version.StartsWith("v")) {
            $version = "v$version"
          }
          echo "VERSION_TAG=$version" >> $env:GITHUB_OUTPUT
          # 新增一步：为了方便构建文件名，我们也输出一个不带“v”的版本号，但文件名仍然会带“v”
          # 这样可以灵活用于文件名（带v）和内部逻辑（不带v），但在这里为了您的需求，我们直接用VERSION_TAG构建文件名
          $fileNameVersion = $version
          echo "FILENAME_VERSION=$fileNameVersion" >> $env:GITHUB_OUTPUT

      - name: Check if version matches latest release tag
        id: check_latest_release
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const currentVersionTag = '${{ steps.get_version.outputs.VERSION_TAG }}';
            let latestReleaseTagName = null;

            try {
              const { data: latestRelease } = await github.rest.repos.getLatestRelease({
                owner: context.repo.owner,
                repo: context.repo.repo,
              });
              if (latestRelease && latestRelease.tag_name) {
                latestReleaseTagName = latestRelease.tag_name;
                console.log(`Latest release tag found: ${latestReleaseTagName}`);
              } else {
                console.log('No latest release found or tag_name is missing.');
              }
            } catch (error) {
              if (error.status === 404) {
                console.log('No releases found in the repository yet. Proceeding to create a new release.');
              } else {
                console.warn(`Could not fetch latest release: ${error.message}. Proceeding with caution.`);
                // Do not fail here, allow the workflow to continue if API fails for other reasons
              }
            }

            if (currentVersionTag && latestReleaseTagName && currentVersionTag === latestReleaseTagName) {
              core.setFailed(`Version ${currentVersionTag} matches the latest release tag (${latestReleaseTagName}). Workflow terminated to prevent duplicate release.`);
            } else {
              console.log(`Version ${currentVersionTag} does not match latest release tag ${latestReleaseTagName} (or no latest release found). Proceeding with release.`);
            }

      - name: Create Git Tag
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const tag = '${{ steps.get_version.outputs.VERSION_TAG }}';
            const owner = context.repo.owner;
            const repo = context.repo.repo;
            const sha = context.sha;
            console.log(`Attempting to create tag ${tag} at commit ${sha}`);
            try {
              await github.rest.git.createRef({
                owner,
                repo,
                ref: `refs/tags/${tag}`,
                sha: sha
              });
              console.log(`Tag ${tag} created successfully.`);
            } catch (error) {
              if (error.message && (error.message.includes('Reference already exists') || error.message.includes('Tag already exists'))) {
                console.warn(`Tag ${tag} already exists on remote. Skipping creation.`);
              } else {
                core.setFailed(`Failed to create tag ${tag}: ${error.message || error}`);
                throw error;
              }
            }

      - name: Fetch all tags again (after potential tag creation)
        run: git fetch --tags --force

      - name: Determine if Pre-release
        id: prerelease_check
        shell: pwsh
        run: |
          $VERSION_TAG = "${{ steps.get_version.outputs.VERSION_TAG }}"
          $IS_PRERELEASE = "false"
          if ($VERSION_TAG -like "*beta*" -or $VERSION_TAG -like "*alpha*" -or $VERSION_TAG -like "*rc*") {
            $IS_PRERELEASE = "true"
          }
          echo "IS_PRERELEASE=$IS_PRERELEASE" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          Write-Host "Release version: ${VERSION_TAG}, Is Prerelease: ${IS_PRERELEASE}"

      - name: Generate Changelog
        id: changelog
        uses: orhun/git-cliff-action@v4
        with:
          config: .github/cliff.toml
          args: --latest --strip header
        env:
          OUTPUT: CHANGES.md
          GITHUB_REPO: ${{ github.repository }}

      - name: Read Changelog Content
        id: read_changelog_content
        shell: pwsh
        run: |
          $CHANGELOG_BODY = Get-Content -Path CHANGES.md -Raw
          $DELIMITER = "EOF_CHANGELOG_BODY"
          "CHANGELOG_BODY<<$DELIMITER" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          $CHANGELOG_BODY | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          "$DELIMITER" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          Write-Host "--- Generated Changelog ---"
          Write-Host "$CHANGELOG_BODY"
          Write-Host "-------------------------"

      - name: Compile AutoHotkey Script with Action
        id: compile_script
        uses: benmusson/ahk2exe-action@v1
        with:
          in: ./DoroHelper.ahk
          # 直接使用 VERSION_TAG 构建文件名
          out: ./DoroHelper_${{ steps.get_version.outputs.VERSION_TAG }}.exe
          icon: ./doro.ico
          target: x64
          ahk-tag: v2.0.19
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2.2.2
        with:
          tag_name: ${{ steps.get_version.outputs.VERSION_TAG }}
          name: ${{ steps.get_version.outputs.VERSION_TAG }}
          body: |
            ${{ steps.read_changelog_content.outputs.CHANGELOG_BODY }}
          draft: false
          prerelease: ${{ steps.prerelease_check.outputs.IS_PRERELEASE }}
          # 直接使用 VERSION_TAG 构建文件名
          files: DoroHelper_${{ steps.get_version.outputs.VERSION_TAG }}.exe
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Trigger MirrorChyanUploading
        shell: bash
        run: |
          gh workflow run --repo $GITHUB_REPOSITORY mirrorchyan_uploading
          gh workflow run --repo $GITHUB_REPOSITORY mirrorchyan_release_note
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
