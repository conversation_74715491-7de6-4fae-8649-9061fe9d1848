[remote.github]
owner = "1204244136" # 替换为你的GitHub用户名
repo = "DoroHelper"     # 替换为你的仓库名

[changelog]
header = """
# 更新日志
"""

body = """
{%- macro remote_url() -%}
  https://github.com/{{ remote.github.owner }}/{{ remote.github.repo }}
{%- endmacro -%}

{% macro print_commit(commit) -%}
    - {% if commit.scope %}*({{ commit.scope }})* {% endif %}\
        {% if commit.breaking %}[**breaking**] {% endif %}\
        {{ commit.message | upper_first }} \
        ({{ commit.id }}) \
        @{{ author.login | default(value=commit.author.name) }}
{% endmacro -%}

{% if version %}\
    ## {{ version | trim_start_matches(pat="v") }} ({{ timestamp | date(format="%Y-%m-%d") }})
{% else %}\
    ## 未发布
{% endif %}\

{% for group, commits in commits | group_by(attribute="group") %}
    ### {{ group | striptags | trim | upper_first }}
    {% for commit in commits
    | filter(attribute="scope")
    | sort(attribute="scope") %}
        {{ self::print_commit(commit=commit) }}
    {%- endfor %}
    {% for commit in commits %}
        {%- if not commit.scope -%}
            {{ self::print_commit(commit=commit) }}
        {% endif -%}
    {% endfor -%}
{% endfor -%}
{%- if github -%}
{% if github.contributors | filter(attribute="is_first_time", value=true) | length != 0 %}
  ## 新贡献者 ❤️
{% endif %}\
{% for contributor in github.contributors | filter(attribute="is_first_time", value=true) %}
  * @{{ contributor.username }} made their first contribution
    {%- if contributor.pr_number %} in \
      [#{{ contributor.pr_number }}]({{ self::remote_url() }}/pull/{{ contributor.pr_number }}) \
    {%- endif %}
{%- endfor -%}
{%- endif %}
"""

footer = """

[已有 Mirror酱 CDK？点击前往高速下载](https://mirrorchyan.com/zh/projects?rid=DoroHelper&source=doro-gh-release)
[如果觉得DoroHelper好用的话，点击这里支持作者](https://github.com/1204244136/DoroHelper?tab=readme-ov-file#%E6%94%AF%E6%8C%81%E5%92%8C%E9%BC%93%E5%8A%B1)
"""

[git]
conventional_commits = true
filter_unconventional = false
commit_parsers = [
    { message = "^feat", group = "新增 | New" },
    { message = "^fix", group = "修复 | Fix" },
    { message = "^perf", group = "改进 | Improved" },
    { message = "^docs", group = "文档 | Docs" },
    { message = "^refactor", group = "重构 | Refactor" },
    { message = "^style", group = "样式 | Style" },
    { message = "^test", group = "测试 | Test" },
    { message = "^build", group = "依赖 | Build" },
    { message = "^chore", group = "维护 | Chore" },
    { message = "^ci", group = "集成 | Ci" },
    { message = "^revert", group = "撤销 | Revert" },
]

ignore_tags = "rc"
sort_commits = "oldest"

[git.github]
commits = true