name: 功能请求 | Feature Request
description: 新功能、建议等
labels: ["enhancement"]
body:
  - type: checkboxes
    id: checks
    attributes:
      label: 在提问之前...
      options:
        - label: 我不是国服玩家
          required: true
        - label: 我使用的是版本号1开头的新版本，而不是版本号0开头的（如0.1.22）的老版本
          required: true
        - label: 在更新至最新版本后，我仍想提出这个请求
          required: true
        - label: 我认为这个请求不只是服务我一个人，有许多人能因此受益
          required: true
        - label: 我认为这个请求具有足够的前瞻性，完成后获得的收益远大于作者的时间成本
          required: true
        - label: 我理解 Issue 是用于反馈和解决问题的，而非吐槽评论区，将尽可能提供更多信息帮助问题解决
          required: true
        - label: 我填写了简短且清晰明确的标题，以便开发者在翻阅 Issue 列表时能快速确定大致问题。而不是“一个建议”“卡住了”等
          required: true
        - label: 我基本确定这是一个新功能/建议，而不是遇到了 Bug（不确定的话请附上日志）
          required: true
        - label: 我已检查了置顶议题（Pinned Issue）（公告）、活跃议题（Open Issue）、已关闭议题（Closed Issue），确认我的建议未被实现或停用
          required: true
  - type: textarea
    id: describe
    attributes:
      label: 说说你遇到的问题？
    validations:
      required: false
  - type: textarea
    id: solution
    attributes:
      label: 有什么好的想法？
    validations:
      required: false
  - type: textarea
    id: additional
    attributes:
      label: 其他内容
      description: 关于该需求建议的任何其他背景或屏幕截图。
    validations:
      required: false
